"use client"

import { useEffect, useMemo } from "react"
import { useActivityPreferencesStore } from "./activity-preferences.store"
import { useIsUserSubscribed } from "../user-subscription/user-subscription.hooks"

/**
 * Hook for real-time activity preferences
 * @param userId User ID
 * @returns Activity preferences with real-time updates
 */
export const useRealtimeActivityPreferences = (userId?: string) => {
  const isSubscribed = useIsUserSubscribed()
  const {
    preferences,
    loading,
    error,
    subscribeToActivityPreferences,
    unsubscribeFromActivityPreferences,
  } = useActivityPreferencesStore()

  // Memoize subscription status to prevent infinite re-renders
  const memoizedIsSubscribed = useMemo(() => isSubscribed, [isSubscribed])
  
  // Memoize userId to prevent infinite re-renders
  const memoizedUserId = useMemo(() => userId, [userId])

  useEffect(() => {
    if (memoizedUserId && memoizedIsSubscribed) {
      // Subscribe to real-time updates
      subscribeToActivityPreferences(memoizedUserId)

      // Cleanup on unmount
      return () => {
        unsubscribeFromActivityPreferences()
      }
    } else {
      // Unsubscribe if user is not subscribed or no userId
      unsubscribeFromActivityPreferences()
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [
    memoizedUserId,
    memoizedIsSubscribed,
    subscribeToActivityPreferences,
    unsubscribeFromActivityPreferences,
  ])

  // Memoize the return value to prevent unnecessary re-renders
  const memoizedPreferences = useMemo(() => {
    if (!memoizedIsSubscribed) return null
    return preferences
  }, [memoizedIsSubscribed, preferences])

  return {
    preferences: memoizedPreferences,
    loading: memoizedIsSubscribed ? loading : false,
    error: memoizedIsSubscribed ? error : null,
  }
}
